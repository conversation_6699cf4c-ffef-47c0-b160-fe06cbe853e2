#include "RecordManager.h"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <filesystem>
#include <ctime>

RecordManager::RecordManager(const ManagerConfig& config)
    : config_(config)
    , status_(RecordStatus::STOPPED)
    , running_(false)
    , paused_(false)
    , retry_count_(0) {
}

RecordManager::~RecordManager() {
    stop();
    cleanup();
}

bool RecordManager::initialize() {
    std::lock_guard<std::mutex> lock(mutex_);

    // 创建输出目录
    if (!createOutputDirectory()) {
        handleError("创建输出目录失败: " + config_.output_dir);
        return false;
    }

    // 初始化屏幕录制器
    ScreenRecorder::RecordConfig record_config;
    record_config.width = config_.width;
    record_config.height = config_.height;
    record_config.fps = config_.fps;
    record_config.bitrate = config_.record_bitrate;

    recorder_ = std::make_unique<ScreenRecorder>(record_config);
    if (!recorder_->initialize()) {
        handleError("初始化屏幕录制器失败");
        return false;
    }

    // 初始化推流器（如果启用）
    if (config_.enable_streaming && !config_.rtmp_url.empty()) {
        StreamPublisher::StreamConfig stream_config;
        stream_config.rtmp_url = config_.rtmp_url;
        stream_config.width = config_.width;
        stream_config.height = config_.height;
        stream_config.fps = config_.fps;
        stream_config.bitrate = config_.stream_bitrate;

        publisher_ = std::make_unique<StreamPublisher>(stream_config);
        if (!publisher_->initialize()) {
            handleError("初始化推流器失败");
            return false;
        }
    }

    log("info", "录制管理器初始化成功");
    return true;
}

bool RecordManager::start() {
    std::lock_guard<std::mutex> lock(mutex_);

    if (running_.load()) {
        log("warning", "录制管理器已在运行中");
        return true;
    }

    status_.store(RecordStatus::STARTING);

    // 生成第一个录制文件名
    current_file_ = generateFileName();

    // 开始录制
    if (!recorder_->startRecording(current_file_)) {
        handleError("开始录制失败");
        status_.store(RecordStatus::ERROR);
        return false;
    }

    // 开始推流（如果启用）
    if (publisher_ && !publisher_->startStreaming()) {
        log("warning", "推流启动失败，但录制将继续");
    }

    running_.store(true);
    paused_.store(false);
    segment_start_time_ = std::chrono::steady_clock::now();
    total_start_time_ = segment_start_time_;
    retry_count_ = 0;

    // 启动管理线程
    manager_thread_ = std::thread(&RecordManager::managerThread, this);

    status_.store(RecordStatus::RECORDING);
    log("info", "录制和推流已启动，文件: " + current_file_);

    // 输出初始配置信息
    std::ostringstream config_info;
    config_info << "配置信息 - 分辨率: " << config_.width << "x" << config_.height
                << " | 帧率: " << config_.fps << "fps"
                << " | 录制码率: " << formatBytes(config_.record_bitrate / 8) << "/s"
                << " | 分段时长: " << formatDuration(config_.segment_duration);
    if (config_.enable_streaming) {
        config_info << " | 推流码率: " << formatBytes(config_.stream_bitrate / 8) << "/s"
                    << " | 推流地址: " << config_.rtmp_url;
    }
    log("info", config_info.str());

    return true;
}

void RecordManager::stop() {
    if (!running_.load()) {
        return;
    }

    status_.store(RecordStatus::STOPPING);
    running_.store(false);

    // 等待管理线程结束
    if (manager_thread_.joinable()) {
        manager_thread_.join();
    }

    // 停止录制和推流
    if (recorder_) {
        recorder_->stopRecording();
    }

    if (publisher_) {
        publisher_->stopStreaming();
    }

    // 添加当前文件到已录制列表
    if (!current_file_.empty()) {
        recorded_files_.push_back(current_file_);
        current_file_.clear();
    }

    status_.store(RecordStatus::STOPPED);
    log("info", "录制和推流已停止");
}

void RecordManager::pause() {
    if (!running_.load() || paused_.load()) {
        return;
    }

    paused_.store(true);

    // 停止录制但保持推流
    if (recorder_) {
        recorder_->stopRecording();
    }

    log("info", "录制已暂停，推流继续");
}

void RecordManager::resume() {
    if (!running_.load() || !paused_.load()) {
        return;
    }

    // 生成新的录制文件
    current_file_ = generateFileName();

    // 恢复录制
    if (recorder_ && recorder_->startRecording(current_file_)) {
        paused_.store(false);
        segment_start_time_ = std::chrono::steady_clock::now();
        log("info", "录制已恢复，新文件: " + current_file_);
    } else {
        handleError("恢复录制失败");
    }
}

void RecordManager::getCurrentFileInfo(std::string& current_file, double& file_duration, double& total_duration) const {
    std::lock_guard<std::mutex> lock(mutex_);

    current_file = current_file_;

    if (recorder_) {
        file_duration = recorder_->getRecordingDuration();
    } else {
        file_duration = 0.0;
    }

    auto now = std::chrono::steady_clock::now();
    auto total_elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - total_start_time_);
    total_duration = total_elapsed.count();
}

void RecordManager::getStreamInfo(bool& is_streaming, int64_t& frames_sent, int64_t& bytes_sent, double& stream_duration) const {
    if (publisher_) {
        is_streaming = publisher_->isStreaming();
        publisher_->getStreamStats(frames_sent, bytes_sent, stream_duration);
    } else {
        is_streaming = false;
        frames_sent = 0;
        bytes_sent = 0;
        stream_duration = 0.0;
    }
}

void RecordManager::setStreamQuality(int quality) {
    if (publisher_) {
        publisher_->setStreamQuality(quality);
        log("info", "推流质量已调整为: " + std::to_string(quality) + "/10");
    }
}

std::vector<std::string> RecordManager::getRecordedFiles() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return recorded_files_;
}

void RecordManager::setErrorCallback(std::function<void(const std::string&)> callback) {
    error_callback_ = callback;
}

void RecordManager::managerThread() {
    log("info", "管理线程已启动");

    // 状态日志计时器
    auto last_status_log = std::chrono::steady_clock::now();
    const auto status_log_interval = std::chrono::seconds(5); // 每5秒输出一次状态

    while (running_.load()) {
        try {
            // 检查是否需要切换文件
            if (!paused_.load() && shouldSwitchFile()) {
                if (!switchToNewFile()) {
                    handleError("切换录制文件失败");
                    if (config_.auto_restart && retry_count_ < config_.max_retry_count) {
                        retry_count_++;
                        log("warning", "尝试重启录制，重试次数: " + std::to_string(retry_count_));
                        std::this_thread::sleep_for(std::chrono::seconds(5));
                        continue;
                    } else {
                        break;
                    }
                }
                retry_count_ = 0; // 重置重试计数
            }

            // 如果有推流器，检查推流状态并同步帧数据
            if (publisher_ && publisher_->isStreaming() && recorder_ && !paused_.load()) {
                AVFrame* frame = nullptr;
                if (recorder_->captureFrame(frame) && frame) {
                    publisher_->pushFrame(frame);
                }
            }

            // 定期输出状态日志
            auto now = std::chrono::steady_clock::now();
            if (now - last_status_log >= status_log_interval) {
                logCurrentStatus();
                last_status_log = now;
            }

            // 短暂休眠以避免过度占用CPU
            std::this_thread::sleep_for(std::chrono::milliseconds(100));

        } catch (const std::exception& e) {
            handleError("管理线程异常: " + std::string(e.what()));
            break;
        }
    }

    log("info", "管理线程已结束");
}

std::string RecordManager::generateFileName() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);

    std::ostringstream oss;
    oss << config_.output_dir << "/"
        << config_.file_prefix << "_"
        << std::put_time(&tm, "%Y%m%d_%H%M%S")
        << ".mp4";

    return oss.str();
}

bool RecordManager::shouldSwitchFile() const {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - segment_start_time_);
    return elapsed.count() >= config_.segment_duration;
}

bool RecordManager::switchToNewFile() {
    log("info", "切换到新的录制文件");

    // 记录当前文件的完成状态
    if (!current_file_.empty()) {
        auto now = std::chrono::steady_clock::now();
        auto file_duration = std::chrono::duration_cast<std::chrono::seconds>(now - segment_start_time_).count();

        // 尝试获取文件大小
        std::string file_size_str = "未知";
        try {
            if (std::filesystem::exists(current_file_)) {
                auto file_size = std::filesystem::file_size(current_file_);
                file_size_str = formatBytes(file_size);
            }
        } catch (const std::exception& e) {
            // 忽略文件大小获取错误
        }

        log("info", "录制文件完成: " + current_file_ +
                   " | 时长: " + formatDuration(file_duration) +
                   " | 大小: " + file_size_str);
    }

    // 停止当前录制
    if (recorder_) {
        recorder_->stopRecording();
    }

    // 添加当前文件到已录制列表
    if (!current_file_.empty()) {
        recorded_files_.push_back(current_file_);
    }

    // 生成新文件名
    current_file_ = generateFileName();

    // 开始新的录制
    if (recorder_ && recorder_->startRecording(current_file_)) {
        segment_start_time_ = std::chrono::steady_clock::now();
        log("info", "新录制文件已启动: " + current_file_ +
                   " | 分段: " + std::to_string(recorded_files_.size() + 1));
        return true;
    } else {
        handleError("启动新录制文件失败: " + current_file_);
        return false;
    }
}

bool RecordManager::createOutputDirectory() const {
    try {
        std::filesystem::create_directories(config_.output_dir);
        return true;
    } catch (const std::exception& e) {
        std::cerr << "创建目录失败: " << e.what() << std::endl;
        return false;
    }
}

void RecordManager::handleError(const std::string& error_msg) {
    log("error", error_msg);

    if (error_callback_) {
        error_callback_(error_msg);
    }

    status_.store(RecordStatus::ERROR);
}

void RecordManager::log(const std::string& level, const std::string& message) const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);

    std::ostringstream oss;
    oss << "[" << std::put_time(&tm, "%Y-%m-%d %H:%M:%S") << "] "
        << "[" << level << "] " << message;

    std::cout << oss.str() << std::endl;
}

void RecordManager::logCurrentStatus() const {
    std::lock_guard<std::mutex> lock(mutex_);

    // 获取录制信息
    std::string current_file;
    double file_duration, total_duration;
    getCurrentFileInfo(current_file, file_duration, total_duration);

    // 获取推流信息
    bool is_streaming;
    int64_t frames_sent, bytes_sent;
    double stream_duration;
    getStreamInfo(is_streaming, frames_sent, bytes_sent, stream_duration);

    // 计算流量速率
    double bitrate_mbps = 0.0;
    if (stream_duration > 0) {
        bitrate_mbps = (bytes_sent * 8.0) / (stream_duration * 1024 * 1024); // Mbps
    }

    // 构建状态消息
    std::ostringstream status_msg;
    status_msg << "状态报告 - ";
    status_msg << "录制: " << formatDuration(file_duration) << "/" << formatDuration(total_duration);

    if (is_streaming) {
        status_msg << " | 推流: " << frames_sent << "帧";
        status_msg << " | 流量: " << formatBytes(bytes_sent);
        status_msg << " | 码率: " << std::fixed << std::setprecision(2) << bitrate_mbps << "Mbps";
        status_msg << " | 推流时长: " << formatDuration(stream_duration);
    } else {
        status_msg << " | 推流: 未启用或已断开";
    }

    log("status", status_msg.str());
}

std::string RecordManager::formatBytes(int64_t bytes) const {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    int unit_index = 0;
    double size = static_cast<double>(bytes);

    while (size >= 1024.0 && unit_index < 4) {
        size /= 1024.0;
        unit_index++;
    }

    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2) << size << " " << units[unit_index];
    return oss.str();
}

std::string RecordManager::formatDuration(double seconds) const {
    int hours = static_cast<int>(seconds) / 3600;
    int minutes = (static_cast<int>(seconds) % 3600) / 60;
    int secs = static_cast<int>(seconds) % 60;

    std::ostringstream oss;
    oss << std::setfill('0') << std::setw(2) << hours << ":"
        << std::setfill('0') << std::setw(2) << minutes << ":"
        << std::setfill('0') << std::setw(2) << secs;
    return oss.str();
}

void RecordManager::cleanup() {
    recorder_.reset();
    publisher_.reset();
    recorded_files_.clear();
    current_file_.clear();
}
