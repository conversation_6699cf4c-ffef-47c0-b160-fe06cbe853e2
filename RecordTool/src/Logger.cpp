#include "Logger.h"
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/rotating_file_sink.h>
#include <filesystem>
#include <iomanip>
#include <sstream>
#include <iostream>

Logger& Logger::getInstance() {
    static Logger instance;
    return instance;
}

bool Logger::initialize(const std::string& log_dir,
                       const std::string& app_name,
                       size_t max_file_size,
                       size_t max_files,
                       Level console_level,
                       Level file_level) {
    try {
        // 创建日志目录
        std::filesystem::create_directories(log_dir);

        // 创建控制台sink
        auto console_sink = std::make_shared<spdlog::sinks::stdout_color_sink_mt>();
        console_sink->set_level(toSpdlogLevel(console_level));
        console_sink->set_pattern("[%Y-%m-%d %H:%M:%S] [%^%l%$] %v");

        // 创建文件sink（轮转日志）
        std::string log_file = log_dir + "/" + app_name + ".log";
        auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
            log_file, max_file_size, max_files);
        file_sink->set_level(toSpdlogLevel(file_level));
        file_sink->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [%l] [%t] %v");

        // 创建状态日志文件sink
        std::string status_log_file = log_dir + "/" + app_name + "_status.log";
        auto status_file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
            status_log_file, max_file_size, max_files);
        status_file_sink->set_level(spdlog::level::info);
        status_file_sink->set_pattern("[%Y-%m-%d %H:%M:%S] %v");

        // 创建多sink日志器
        std::vector<spdlog::sink_ptr> sinks{console_sink, file_sink};
        logger_ = std::make_shared<spdlog::logger>(app_name, sinks.begin(), sinks.end());
        
        // 设置日志级别
        logger_->set_level(spdlog::level::trace);
        logger_->flush_on(spdlog::level::warn);

        // 注册日志器
        spdlog::register_logger(logger_);

        // 创建专门的状态日志器
        auto status_logger = std::make_shared<spdlog::logger>(app_name + "_status", status_file_sink);
        status_logger->set_level(spdlog::level::info);
        spdlog::register_logger(status_logger);

        initialized_ = true;
        
        info("日志系统初始化成功");
        info("日志文件: " + log_file);
        info("状态日志文件: " + status_log_file);
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "日志系统初始化失败: " << e.what() << std::endl;
        return false;
    }
}

void Logger::info(const std::string& message) {
    if (logger_) {
        logger_->info(message);
    }
}

void Logger::debug(const std::string& message) {
    if (logger_) {
        logger_->debug(message);
    }
}

void Logger::warn(const std::string& message) {
    if (logger_) {
        logger_->warn(message);
    }
}

void Logger::error(const std::string& message) {
    if (logger_) {
        logger_->error(message);
    }
}

void Logger::status(const std::string& message) {
    if (logger_) {
        logger_->info("[STATUS] " + message);
        
        // 同时写入状态日志文件
        auto status_logger = spdlog::get("RecordTool_status");
        if (status_logger) {
            status_logger->info(message);
        }
    }
}

void Logger::logStreamStatus(int64_t frames_sent, int64_t bytes_sent, 
                           double bitrate_mbps, double duration_seconds) {
    std::ostringstream oss;
    oss << "推流状态 - 帧数: " << frames_sent 
        << " | 流量: " << formatBytes(bytes_sent)
        << " | 码率: " << std::fixed << std::setprecision(2) << bitrate_mbps << " Mbps"
        << " | 时长: " << formatDuration(duration_seconds);
    
    status(oss.str());
}

void Logger::logFileStatus(const std::string& filename, int64_t file_size,
                         double duration_seconds, const std::string& action) {
    std::ostringstream oss;
    oss << "文件" << action << " - " << filename
        << " | 大小: " << formatBytes(file_size)
        << " | 时长: " << formatDuration(duration_seconds);
    
    status(oss.str());
}

void Logger::setLevel(Level level) {
    if (logger_) {
        logger_->set_level(toSpdlogLevel(level));
    }
}

void Logger::flush() {
    if (logger_) {
        logger_->flush();
    }
}

spdlog::level::level_enum Logger::toSpdlogLevel(Level level) {
    switch (level) {
        case Level::TRACE:    return spdlog::level::trace;
        case Level::DEBUG:    return spdlog::level::debug;
        case Level::INFO:     return spdlog::level::info;
        case Level::WARN:     return spdlog::level::warn;
        case Level::ERROR:    return spdlog::level::err;
        case Level::CRITICAL: return spdlog::level::critical;
        default:              return spdlog::level::info;
    }
}

std::string Logger::formatBytes(int64_t bytes) {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    int unit_index = 0;
    double size = static_cast<double>(bytes);
    
    while (size >= 1024.0 && unit_index < 4) {
        size /= 1024.0;
        unit_index++;
    }
    
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(2) << size << " " << units[unit_index];
    return oss.str();
}

std::string Logger::formatDuration(double seconds) {
    int hours = static_cast<int>(seconds) / 3600;
    int minutes = (static_cast<int>(seconds) % 3600) / 60;
    int secs = static_cast<int>(seconds) % 60;
    
    std::ostringstream oss;
    oss << std::setfill('0') << std::setw(2) << hours << ":"
        << std::setfill('0') << std::setw(2) << minutes << ":"
        << std::setfill('0') << std::setw(2) << secs;
    return oss.str();
}
