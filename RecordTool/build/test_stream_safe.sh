#!/bin/bash

echo "=== 安全推流测试（仅测试模式）==="

# 强制使用测试模式，避免 X11 问题
export DISPLAY=""

echo "使用测试模式（无屏幕捕获）"
echo "推流目标: rtmp://10.10.22.27/live/livestream"
echo ""

# 测试推流功能（不使用真实屏幕捕获）
echo "启动 10 秒推流测试..."
timeout 10s ./RecordTool --rtmp rtmp://10.10.22.27/live/livestream --duration 10 --width 1280 --height 720 --fps 25

echo ""
echo "测试完成！"

# 检查生成的录制文件
echo ""
echo "生成的录制文件："
ls -la recordings/ | tail -3
