#!/bin/bash

echo "=== 测试日志输出功能 ==="

# 使用测试模式，避免 X11 问题
export DISPLAY=""

echo "启动带日志的推流测试..."
echo "- 每5秒输出状态报告"
echo "- 记录文件更新状态"
echo "- 显示流量和码率信息"
echo ""

# 测试30秒，观察日志输出
timeout 30s ./RecordTool --rtmp rtmp://10.10.22.27/live/livestream \
                         --duration 15 \
                         --width 1280 \
                         --height 720 \
                         --fps 25 \
                         --stream-bitrate 1500000 \
                         --record-bitrate 2000000

echo ""
echo "测试完成！"

# 显示生成的文件
echo ""
echo "生成的录制文件："
ls -lh recordings/ | tail -5
