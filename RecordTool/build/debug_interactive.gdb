# 交互式 GDB 调试脚本
set environment DISPLAY=:1
set args --rtmp rtmp://10.10.22.27/live/livestream --duration 10 --width 1280 --height 720 --fps 25

# 设置更详细的断点
break main
break ScreenRecorder::initialize
break ScreenRecorder::captureFrame
break ScreenRecorder::cleanup
break StreamPublisher::initialize
break RecordManager::start

# 捕获所有信号
catch signal SIGSEGV
catch signal SIGABRT
catch signal SIGFPE

# 设置调试选项
set print pretty on
set print array on
set print object on

# 显示线程信息
set scheduler-locking off

echo "=== 开始调试 RecordTool ===\n"
echo "断点已设置，输入 'continue' 开始运行\n"
echo "当程序崩溃时，使用以下命令:\n"
echo "  bt - 显示调用栈\n"
echo "  info threads - 显示所有线程\n"
echo "  thread apply all bt - 显示所有线程的调用栈\n"
echo "  print variable_name - 打印变量值\n"
echo "================================\n"
