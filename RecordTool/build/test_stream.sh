#!/bin/bash

echo "=== 推流测试脚本 ==="

# 清理之前的进程
pkill -f "ffmpeg.*rtmp"
pkill -f "vlc.*udp"

# 创建命名管道用于 RTMP 到 UDP 转换
PIPE_FILE="/tmp/stream_pipe.flv"
rm -f "$PIPE_FILE"
mkfifo "$PIPE_FILE"

echo "1. 启动 FFmpeg RTMP 接收器..."
# 启动 FFmpeg 接收 RTMP 并转换为 UDP
ffmpeg -f flv -listen 1 -i rtmp://localhost:1935/live/test -c copy -f mpegts udp://localhost:1234 &
FFMPEG_PID=$!

sleep 2

echo "2. 启动 VLC 接收 UDP 流..."
# 启动 VLC 接收 UDP 流
vlc --intf dummy udp://@:1234 &
VLC_PID=$!

sleep 2

echo "3. 启动录制工具推流..."
# 启动我们的录制工具
./RecordTool --rtmp rtmp://localhost:1935/live/test --duration 30 --width 1280 --height 720 --fps 25 &
RECORD_PID=$!

echo "推流测试已启动！"
echo "FFmpeg PID: $FFMPEG_PID"
echo "VLC PID: $VLC_PID" 
echo "RecordTool PID: $RECORD_PID"
echo ""
echo "按任意键停止测试..."
read -n 1

echo "停止所有进程..."
kill $RECORD_PID 2>/dev/null
kill $VLC_PID 2>/dev/null  
kill $FFMPEG_PID 2>/dev/null

rm -f "$PIPE_FILE"
echo "测试完成！"
